import { isLighthouseOrBot, isServerSide, isClientSide, conditionalLoad } from '~/utils'

export const useLighthouseDetection = () => {
    const isLighthouse = ref(false)
    const isBot = ref(false)
    const environment = ref<'server' | 'client' | 'unknown'>('unknown')
    
    // Server-side detection
    if (isServerSide()) {
        environment.value = 'server'
        const headers = useRequestHeaders()
        const userAgent = headers['user-agent'] || ''
        
        isLighthouse.value = isLighthouseOrBot(userAgent)
        isBot.value = isLighthouse.value
    }
    
    // Client-side detection
    onMounted(() => {
        if (isClientSide()) {
            environment.value = 'client'
            const userAgent = navigator?.userAgent || ''
            
            isLighthouse.value = isLighthouseOrBot(userAgent)
            isBot.value = isLighthouse.value
        }
    })
    
    /**
     * Execute code conditionally based on environment and bot detection
     */
    const executeConditionally = (options: {
        onServer?: () => void
        onClient?: () => void
        onLighthouse?: () => void
        onNormalUser?: () => void
    }) => {
        const headers = isServerSide() ? useRequestHeaders() : {}
        const userAgent = isServerSide() 
            ? headers['user-agent'] || ''
            : (typeof navigator !== 'undefined' ? navigator.userAgent : '')
            
        conditionalLoad({
            ...options,
            userAgent
        })
    }
    
    /**
     * Load script only for normal users (not bots/lighthouse)
     */
    const loadScriptForUsers = (scriptSrc: string, options?: {
        async?: boolean
        defer?: boolean
        type?: string
    }) => {
        executeConditionally({
            onNormalUser: () => {
                if (typeof document !== 'undefined') {
                    const script = document.createElement('script')
                    script.src = scriptSrc
                    script.async = options?.async ?? true
                    script.defer = options?.defer ?? false
                    script.type = options?.type ?? 'text/javascript'
                    document.head.appendChild(script)
                }
            },
            onLighthouse: () => {
                // Skip loading for Lighthouse/bots
                console.log(`Skipping script ${scriptSrc} for Lighthouse/bot`)
            }
        })
    }
    
    /**
     * Load CSS only for normal users
     */
    const loadStyleForUsers = (href: string, media?: string) => {
        executeConditionally({
            onNormalUser: () => {
                if (typeof document !== 'undefined') {
                    const link = document.createElement('link')
                    link.rel = 'stylesheet'
                    link.href = href
                    if (media) link.media = media
                    document.head.appendChild(link)
                }
            },
            onLighthouse: () => {
                console.log(`Skipping stylesheet ${href} for Lighthouse/bot`)
            }
        })
    }
    
    /**
     * Execute function only for normal users
     */
    const executeForUsers = (fn: () => void) => {
        executeConditionally({
            onNormalUser: fn,
            onLighthouse: () => {
                console.log('Skipping function execution for Lighthouse/bot')
            }
        })
    }
    
    /**
     * Execute function only for Lighthouse/bots
     */
    const executeForLighthouse = (fn: () => void) => {
        executeConditionally({
            onLighthouse: fn,
            onNormalUser: () => {
                console.log('Skipping function execution for normal user')
            }
        })
    }
    
    return {
        isLighthouse: readonly(isLighthouse),
        isBot: readonly(isBot),
        environment: readonly(environment),
        executeConditionally,
        loadScriptForUsers,
        loadStyleForUsers,
        executeForUsers,
        executeForLighthouse
    }
}
