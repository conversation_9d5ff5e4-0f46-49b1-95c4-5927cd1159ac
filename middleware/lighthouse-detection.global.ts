import { isLighthouseOrBot } from '~/utils'

export default defineNuxtRouteMiddleware((to, from) => {
    if (import.meta.server) {
        const headers = useRequestHeaders()
        const userAgent = headers['user-agent'] || ''
        
        // Set lighthouse detection flag in nuxt state
        const nuxtApp = useNuxtApp()
        nuxtApp.ssrContext = nuxtApp.ssrContext || {}
        nuxtApp.ssrContext.isLighthouse = isLighthouseOrBot(userAgent)
        
        // Set cookie for client-side detection
        const lighthouseCookie = useCookie('is-lighthouse', {
            default: () => false,
            maxAge: 60 * 5, // 5 minutes
            sameSite: 'lax'
        })
        
        lighthouseCookie.value = isLighthouseOrBot(userAgent)
        
        // Log detection for debugging
        if (isLighthouseOrBot(userAgent)) {
            console.log(`🤖 Lighthouse/Bot detected: ${userAgent}`)
        }
    }
})
