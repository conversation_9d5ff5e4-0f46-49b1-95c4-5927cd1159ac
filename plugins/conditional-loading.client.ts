export default defineNuxtPlugin(() => {
    const { executeConditionally, loadScriptForUsers, executeForUsers } =
        useLighthouseDetection()

    // Example: Load analytics only for normal users
    executeForUsers(() => {
        // Load Google Analytics or other tracking scripts
        const config = useRuntimeConfig()

        if (config.public.GOOGLE_ANALYTICS_ID) {
            console.log('Loading analytics for normal user')
            // Analytics will be loaded by nuxt-gtag module
        }
    })

    // Example: Load chat widget only for normal users
    executeForUsers(() => {
        const config = useRuntimeConfig()

        if (config.public.LIVE_CHAT_ID) {
            console.log('Loading chat widget for normal user')
            // Load chat widget script here
        }
    })

    // Example: Skip heavy animations for Lighthouse
    executeConditionally({
        onLighthouse: () => {
            // Disable animations for better performance scores
            const style = document.createElement('style')
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                    scroll-behavior: auto !important;
                }
            `
            document.head.appendChild(style)
            console.log('🚀 Animations disabled for Lighthouse')
        },
        onNormalUser: () => {
            console.log('✨ Animations enabled for normal user')
        },
    })

    // Example: Load non-critical resources only for normal users
    executeForUsers(() => {
        // Load social media widgets, ads, etc.
        setTimeout(() => {
            console.log('Loading non-critical resources for normal user')
            // Load non-critical scripts here
        }, 2000)
    })
})
