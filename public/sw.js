const CACHE_NAME = 'z01sv-cache-v2'
const STATIC_CACHE_NAME = 'z01sv-static-v2'
const DYNAMIC_CACHE_NAME = 'z01sv-dynamic-v2'
const CRITICAL_CACHE_NAME = 'z01sv-critical-v2'

// Critical assets that must be cached immediately with long TTL
const CRITICAL_ASSETS = [
    '/',
    '/assets/scss/style.scss',
    '/assets/favicon/favicon.ico',
    // Critical hero banner images only (these will be cached for 1 year)
    '/assets/images/home/<USER>/jackpot/pc.avif',
    '/assets/images/home/<USER>/jackpot/pc.webp',
    '/assets/images/home/<USER>/jackpot/pc.jpg',
    '/assets/images/home/<USER>/jackpot/mb.avif',
    '/assets/images/home/<USER>/jackpot/mb.webp',
    '/assets/images/home/<USER>/jackpot/mb.jpg',
]

// Static assets for long-term caching (1 year TTL)
const LONG_TERM_CACHE_ASSETS = [
    // Removed nanoplayer - will be loaded dynamically when needed
    // Hero banner images - cache for 1 year
    '/assets/images/home/<USER>/danh-de-mien-phi/pc.png',
    '/assets/images/home/<USER>/danh-de-mien-phi/pc.avif',
    '/assets/images/home/<USER>/danh-de-mien-phi/pc.webp',
    '/assets/images/home/<USER>/danh-de-mien-phi/mb.png',
    '/assets/images/home/<USER>/danh-de-mien-phi/mb.avif',
    '/assets/images/home/<USER>/danh-de-mien-phi/mb.webp',
]

const CACHEABLE_EXTENSIONS = [
    '.css',
    '.js',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.svg',
    '.webp',
    '.avif',
    // '.woff',
    // '.woff2',
    '.ttf',
    '.eot',
    '.ico',
]

// domain allowed to cache (fonts, analytics, CDN)
const ALLOWED_DOMAINS = [
    'fonts.googleapis.com', // Google Fonts CSS
    'fonts.gstatic.com', // Google Fonts files
    'www.googletagmanager.com', // Google Analytics
    'www.google-analytics.com', // Google Analytics
    'ssl.google-analytics.com', // Google Analytics SSL
    'cdnjs.cloudflare.com', // Cloudflare CDN
    'unpkg.com', // UNPKG CDN
    'jsdelivr.net', // jsDelivr CDN
    'cdn.jsdelivr.net', // jsDelivr CDN
]

// Install event - cache critical assets first, then static assets
self.addEventListener('install', (event) => {
    console.log('[SW] Installing service worker...')
    event.waitUntil(
        // Cache critical assets first
        caches
            .open(CRITICAL_CACHE_NAME)
            .then((criticalCache) => {
                console.log('[SW] Caching critical assets')
                return criticalCache.addAll(CRITICAL_ASSETS)
            })
            .then(() => {
                console.log('[SW] Critical assets cached successfully')
                // Then cache long-term assets
                return caches.open(STATIC_CACHE_NAME)
            })
            .then((staticCache) => {
                console.log('[SW] Caching long-term assets')
                return staticCache.addAll(LONG_TERM_CACHE_ASSETS)
            })
            .then(() => {
                console.log('[SW] Static assets cached successfully')
                return self.skipWaiting()
            })
            .catch((error) => {
                console.error('[SW] Failed to cache assets:', error)
            })
    )
})

// Activate event - cleanup old caches
self.addEventListener('activate', (event) => {
    console.log('[SW] Activating service worker...')
    event.waitUntil(
        caches
            .keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (
                            cacheName !== STATIC_CACHE_NAME &&
                            cacheName !== DYNAMIC_CACHE_NAME
                        ) {
                            console.log('[SW] Deleting old cache:', cacheName)
                            return caches.delete(cacheName)
                        }
                    })
                )
            })
            .then(() => {
                console.log('[SW] Service worker activated')
                return self.clients.claim()
            })
    )
})

// Fetch event - intercept network requests
self.addEventListener('fetch', (event) => {
    const { request } = event
    const url = new URL(request.url)

    // Skip request that not need to cache
    if (
        request.method !== 'GET' ||
        request.headers.get('range') ||
        url.protocol === 'chrome-extension:' ||
        url.protocol === 'moz-extension:'
    ) {
        return
    }

    // Cache strategy for external allowed resources (fonts, analytics)
    if (isExternalAllowedResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Special strategy for CSS files to prevent render blocking
    else if (isCSSResource(url)) {
        event.respondWith(cssOptimizedStrategy(request))
    }
    // Optimized cache strategy for JS based on type
    else if (isJSResource(url)) {
        event.respondWith(jsOptimizedStrategy(request))
    }
    // Cache strategy for other cacheable resources
    else if (isCacheableResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Cache strategy for images
    else if (isImageResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Cache strategy for fonts
    else if (isFontResource(url)) {
        event.respondWith(cacheFirstStrategy(request))
    }
    // Network first for API calls
    else if (isApiRequest(url)) {
        event.respondWith(networkFirstStrategy(request))
    }
    // Stale while revalidate for HTML pages
    else {
        event.respondWith(staleWhileRevalidateStrategy(request))
    }
})

// Check if resource is CSS
function isCSSResource(url) {
    return url.pathname.endsWith('.css') || url.pathname.includes('.css')
}

// Check if resource is JavaScript
function isJSResource(url) {
    return url.pathname.endsWith('.js') || url.pathname.includes('.js')
}

// Check if JavaScript is critical (main app bundle)
function isCriticalJS(url) {
    const pathname = url.pathname
    return (
        pathname.includes('/entry.') ||
        pathname.includes('/app.') ||
        pathname.includes('/main.') ||
        pathname.includes('/runtime.') ||
        pathname.includes('/vue.') ||
        pathname.includes('/_nuxt/entry') ||
        pathname.includes('/_nuxt/app')
    )
}

// Check if JavaScript is vendor library
function isVendorJS(url) {
    const pathname = url.pathname
    return (
        pathname.includes('/vendor') ||
        pathname.includes('/vendors') ||
        pathname.includes('/chunk-vendors') ||
        pathname.includes('/_nuxt/vendor') ||
        pathname.includes('/_nuxt/vendors')
    )
}

// Check if JavaScript is lazy-loaded chunk
function isLazyJS(url) {
    const pathname = url.pathname
    return (
        pathname.includes('/chunk-') ||
        pathname.includes('/lazy-') ||
        pathname.includes('/_nuxt/chunk-') ||
        pathname.includes('/_nuxt/lazy-') ||
        /\/_nuxt\/[a-f0-9]{8}\.js$/.test(pathname) // Hash-based chunks
    )
}

// Check if resource can be cached
function isCacheableResource(url) {
    return (
        CACHEABLE_EXTENSIONS.some((ext) => url.pathname.endsWith(ext)) ||
        url.pathname.includes('/_nuxt/') ||
        url.pathname.includes('/assets/')
    )
}

// Check if resource is image
function isImageResource(url) {
    const imageExtensions = [
        '.png',
        '.jpg',
        '.jpeg',
        '.gif',
        '.svg',
        '.webp',
        '.avif',
    ]
    return (
        imageExtensions.some((ext) => url.pathname.endsWith(ext)) ||
        url.pathname.includes('/assets/images/')
    )
}

// Check if resource is font
function isFontResource(url) {
    const fontExtensions = ['.woff', '.woff2', '.ttf', '.eot']
    return (
        fontExtensions.some((ext) => url.pathname.endsWith(ext)) ||
        ALLOWED_DOMAINS.some((domain) => url.hostname.includes(domain))
    )
}

// Check if it's an external resource from allowed domains
function isExternalAllowedResource(url) {
    return ALLOWED_DOMAINS.some((domain) => url.hostname.includes(domain))
}

// Check if resource is API request
function isApiRequest(url) {
    return (
        url.pathname.startsWith('/api/') ||
        url.pathname.startsWith('/api-promotion/')
    )
}

// Cache First Strategy - priority cache, fallback to network
async function cacheFirstStrategy(request) {
    try {
        const cachedResponse = await caches.match(request)
        if (cachedResponse) {
            return cachedResponse
        }

        const networkResponse = await fetch(request)
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME)
            cache.put(request, networkResponse.clone())
        }
        return networkResponse
    } catch (error) {
        console.error('[SW] Cache first strategy failed:', error)
        return new Response('Network error', { status: 503 })
    }
}

// Network First Strategy - priority network, fallback to cache
async function networkFirstStrategy(request) {
    try {
        const networkResponse = await fetch(request)
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME)
            cache.put(request, networkResponse.clone())
        }
        return networkResponse
    } catch (error) {
        console.error('[SW] Network first strategy failed:', error)
        const cachedResponse = await caches.match(request)
        if (cachedResponse) {
            return cachedResponse
        }
        return new Response('Network error', { status: 503 })
    }
}

// JavaScript Optimized Strategy - prioritize critical JS and optimize caching
async function jsOptimizedStrategy(request) {
    const url = new URL(request.url)
    const pathname = url.pathname

    if (isCriticalJS(url)) {
        // Critical JS: Cache first with high priority
        try {
            const cachedResponse = await caches.match(request)
            if (cachedResponse) {
                console.log('[SW] Serving critical JS from cache:', pathname)
                return cachedResponse
            }

            const networkResponse = await fetch(request)
            if (networkResponse.ok) {
                const cache = await caches.open(CRITICAL_CACHE_NAME)
                cache.put(request, networkResponse.clone())
                console.log('[SW] Cached critical JS:', pathname)
            }
            return networkResponse
        } catch (error) {
            console.error('[SW] Critical JS strategy failed:', error)
            return new Response('// Critical JS failed to load', {
                status: 503,
                headers: { 'Content-Type': 'application/javascript' },
            })
        }
    } else if (isVendorJS(url)) {
        // Vendor JS: Long-term cache with cache first strategy
        try {
            const cachedResponse = await caches.match(request)
            if (cachedResponse) {
                console.log('[SW] Serving vendor JS from cache:', pathname)
                return cachedResponse
            }

            const networkResponse = await fetch(request)
            if (networkResponse.ok) {
                const cache = await caches.open(STATIC_CACHE_NAME)
                cache.put(request, networkResponse.clone())
                console.log('[SW] Cached vendor JS:', pathname)
            }
            return networkResponse
        } catch (error) {
            console.error('[SW] Vendor JS strategy failed:', error)
            const cachedResponse = await caches.match(request)
            return (
                cachedResponse ||
                new Response('// Vendor JS failed to load', {
                    status: 503,
                    headers: { 'Content-Type': 'application/javascript' },
                })
            )
        }
    } else if (isLazyJS(url)) {
        // Lazy JS: Stale while revalidate for dynamic chunks
        console.log('[SW] Handling lazy JS chunk:', pathname)
        return staleWhileRevalidateStrategy(request)
    } else {
        // Other JS: Default cache strategy
        return cacheFirstStrategy(request)
    }
}

// CSS Optimized Strategy - prioritize critical CSS using dynamic patterns
async function cssOptimizedStrategy(request) {
    const url = new URL(request.url)
    const pathname = url.pathname

    // Dynamic detection of critical CSS using patterns
    const isCriticalCSS =
        pathname.includes('/entry.') ||
        pathname.includes('/index.') ||
        pathname.includes('/app.') ||
        pathname.includes('/main.') ||
        pathname.includes('/critical.')

    // Dynamic detection of non-critical CSS
    const isNonCriticalCSS =
        pathname.includes('swiper') ||
        pathname.includes('icon') ||
        pathname.includes('button') ||
        pathname.includes('login') ||
        pathname.includes('default') ||
        pathname.includes('modal') ||
        pathname.includes('tooltip')

    if (isCriticalCSS) {
        // Critical CSS: Cache first with immediate response
        try {
            const cachedResponse = await caches.match(request)
            if (cachedResponse) {
                console.log('[SW] Serving critical CSS from cache:', pathname)
                return cachedResponse
            }

            const networkResponse = await fetch(request)
            if (networkResponse.ok) {
                const cache = await caches.open(CRITICAL_CACHE_NAME)
                cache.put(request, networkResponse.clone())
                console.log('[SW] Cached critical CSS:', pathname)
            }
            return networkResponse
        } catch (error) {
            console.error('[SW] Critical CSS strategy failed:', error)
            // Return minimal CSS to prevent render blocking
            return new Response(
                `
               
            `,
                {
                    headers: { 'Content-Type': 'text/css' },
                }
            )
        }
    } else if (isNonCriticalCSS) {
        // Non-critical CSS: Stale while revalidate with lower priority
        console.log('[SW] Handling non-critical CSS:', pathname)
        return staleWhileRevalidateStrategy(request)
    } else {
        // Unknown CSS: Default cache strategy
        return cacheFirstStrategy(request)
    }
}

// Stale While Revalidate Strategy - return cache immediately, update cache in background
async function staleWhileRevalidateStrategy(request) {
    const cache = await caches.open(DYNAMIC_CACHE_NAME)
    const cachedResponse = await cache.match(request)

    const fetchPromise = fetch(request)
        .then((networkResponse) => {
            if (networkResponse.ok) {
                cache.put(request, networkResponse.clone())
            }
            return networkResponse
        })
        .catch((error) => {
            console.error('[SW] Stale while revalidate failed:', error)
            return (
                cachedResponse || new Response('Network error', { status: 503 })
            )
        })

    return cachedResponse || fetchPromise
}

// Background sync cho offline functionality
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        console.log('[SW] Background sync triggered')
        event.waitUntil(doBackgroundSync())
    }
})

function doBackgroundSync() {
    try {
        // Perform background sync when network is available
        console.log('[SW] Performing background sync...')
        return Promise.resolve()
    } catch (error) {
        console.error('[SW] Background sync failed:', error)
        return Promise.reject(error)
    }
}

// Push notification handling
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json()
        const options = {
            body: data.body || 'Bạn có thông báo mới!',
            icon: '/assets/favicon/favicon.ico',
            badge: '/assets/favicon/favicon.ico',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: 1,
            },
            actions: [
                {
                    action: 'explore',
                    title: 'Xem chi tiết',
                    icon: '/assets/favicon/favicon.ico',
                },
                {
                    action: 'close',
                    title: 'Đóng',
                    icon: '/assets/favicon/favicon.ico',
                },
            ],
        }

        event.waitUntil(
            self.registration.showNotification(data.title || 'Z01', options)
        )
    }
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    event.notification.close()

    if (event.action === 'explore') {
        event.waitUntil(clients.openWindow('/'))
    }
})

// Message handling from main thread
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting()
    }

    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME })
    }
})
