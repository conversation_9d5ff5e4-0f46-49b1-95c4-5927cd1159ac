export const isLighthouseOrBot = (): boolean => {
  // Server-side detection
  if (typeof window === 'undefined') {
    const headers = useRequestHeaders()
    const userAgent = headers['user-agent']?.toLowerCase() || ''
    
    // Detect Lighthouse và các bot khác từ headers
    const botPatterns = [
      'lighthouse',
      'chrome-lighthouse',
      'googlebot',
      'bingbot',
      'yandexbot',
      'baiduspider',
      'facebookexternalhit',
      'twitterbot',
      'linkedinbot',
      'whatsapp',
      'telegrambot',
      'slackbot',
      'discordbot',
      'headless',
      'phantomjs',
      'selenium',
      'webdriver',
      'crawler',
      'spider',
      'bot',
      'scraper'
    ]
    
    return botPatterns.some(pattern => userAgent.includes(pattern))
  }

  // Client-side detection
  const userAgent = navigator.userAgent.toLowerCase()
  
  // Detect Lighthouse
  if (userAgent.includes('lighthouse') || userAgent.includes('chrome-lighthouse')) {
    return true
  }

  // Detect other common bots
  const botPatterns = [
    'googlebot',
    'bingbot',
    'yandexbot',
    'baiduspider',
    'facebookexternalhit',
    'twitterbot',
    'linkedinbot',
    'whatsapp',
    'telegrambot',
    'slackbot',
    'discordbot',
    'headless',
    'phantomjs',
    'selenium',
    'webdriver',
    'crawler',
    'spider',
    'bot',
    'scraper'
  ]

  return botPatterns.some(pattern => userAgent.includes(pattern))
}

export const shouldSkipSocketOperations = (): boolean => {
  return isLighthouseOrBot()
} 